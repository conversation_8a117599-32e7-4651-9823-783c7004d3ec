"use client";

import ModelSelector from "@/components/ModelSelector";
import FileUploader from "@/components/FileUploader";
import MessageDisplay from "@/components/MessageDisplay";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Revision } from "@/app/api/recognize/route";
import { MODELS } from "@/lib/const";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

export default function Home() {
  const [filesBefore, setFilesBefore] = useState<FileList | null>(null);
  const [filesAfter, setFilesAfter] = useState<FileList | null>(null);
  const [imagesBefore, setImagesBefore] = useState<string[]>([]);
  const [imagesAfter, setImagesAfter] = useState<string[]>([]);
  const [currentPageBefore, setCurrentPageBefore] = useState(0);
  const [currentPageAfter, setCurrentPageAfter] = useState(0);
  const [analysis, setAnalysis] = useState<Revision[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [model, setModel] = useState(MODELS.OpenAI[0].id);
  const [isLoadingImages, setIsLoadingImages] = useState(false);

  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "before" | "after"
  ) => {
    if (e.target.files && e.target.files.length > 0) {
      const firstFile = e.target.files[0];
      console.log("type", type);
      let setFiles: React.Dispatch<React.SetStateAction<FileList | null>>;
      let setImages: React.Dispatch<React.SetStateAction<string[]>>;
      let setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
      if (type === "before") {
        console.log("before");
        setFiles = setFilesBefore;
        setImages = setImagesBefore;
        setCurrentPage = setCurrentPageBefore;
      } else {
        console.log("after");
        setFiles = setFilesAfter;
        setImages = setImagesAfter;
        setCurrentPage = setCurrentPageAfter;
      }

      // Automatically detect file type
      if (firstFile.type === "application/pdf") {
        // Handle PDF upload (single file)
        if (e.target.files.length > 1) {
          setError("PDFファイルは1つのみ選択してください");
          setFiles(null);
          setImages([]);
          setAnalysis([]);
          setCurrentPage(0);
          return;
        }

        setFiles(e.target.files);
        // Convert PDF to images
        setIsLoadingImages(true);
        try {
          const formData = new FormData();
          formData.append("file", firstFile);

          const response = await fetch("/api/pdf-to-images", {
            method: "POST",
            body: formData,
          });

          if (!response.ok) {
            throw new Error("PDF画像変換に失敗しました");
          }

          const data = await response.json();
          setImages(data.images);
        } catch (err) {
          setError(
            err instanceof Error ? err.message : "PDF変換エラーが発生しました"
          );
          setImages([]);
        } finally {
          setIsLoadingImages(false);
        }
        setAnalysis([]);
        setCurrentPage(0);
      } else if (
        ["image/jpeg", "image/png", "image/webp"].includes(firstFile.type)
      ) {
        // Handle image upload (multiple files allowed)
        const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
        const areValidImages = Array.from(e.target.files).every((file) =>
          allowedTypes.includes(file.type)
        );

        if (areValidImages) {
          setFiles(e.target.files);
          setError(null);
          setImages(
            Array.from(e.target.files).map((file) => URL.createObjectURL(file))
          );
          setAnalysis([]);
          setCurrentPage(0);
        } else {
          setError(
            "対応する画像ファイル（JPEG、PNG、WEBP）のみを選択してください"
          );
          setFiles(null);
          setImages([]);
          setAnalysis([]);
          setCurrentPage(0);
        }
      } else {
        setError(
          "PDFファイルまたは画像ファイル（JPEG、PNG、WEBP）を選択してください"
        );
        setFiles(null);
        setImages([]);
        setAnalysis([]);
        setCurrentPage(0);
      }
    }
  };

  const handleSubmitBefore = async (e: React.FormEvent) => {
    e.preventDefault();
    if (imagesBefore.length === 0) return;
    console.log("submit");

    setIsAnalyzing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("model", model);
      formData.append("image", imagesBefore[currentPageBefore]);

      // const response = await fetch("/api/recognize", {
      //   method: "POST",
      //   body: formData,
      // });

      // if (!response.ok) {
      //   throw new Error("ファイルの解析に失敗しました");
      // }

      // const data = await response.json();
      // setAnalysis((prev) => {
      //   prev[currentPage] = data.analysis;
      //   return prev;
      // });
    } catch (err) {
      setError(err instanceof Error ? err.message : "エラーが発生しました");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSubmitAfter = async (e: React.FormEvent) => {
    e.preventDefault();
    if (imagesAfter.length === 0) return;
    console.log("submit after");

    setIsAnalyzing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("model", model);
      formData.append("image", imagesAfter[currentPageAfter]);

      // const response = await fetch("/api/recognize", {
      //   method: "POST",
      //   body: formData,
      // });

      // if (!response.ok) {
      //   throw new Error("ファイルの解析に失敗しました");
      // }

      // const data = await response.json();
      // setAnalysis((prev) => {
      //   prev[currentPage] = data.analysis;
      //   return prev;
      // });
    } catch (err) {
      setError(err instanceof Error ? err.message : "エラーが発生しました");
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12 flex flex-col items-center justify-center">
      <ModelSelector model={model} setModel={setModel} />
      <div className="flex justify-center gap-6 w-full">
        <form
          onSubmit={handleSubmitBefore}
          className="flex flex-col gap-6 max-w-md"
        >
          <h2 className="text-xl font-bold">校正前ファイル</h2>
          <FileUploader
            files={filesBefore}
            handleFileChange={(e) => handleFileChange(e, "before")}
          />
          {imagesBefore.length > 0 && (
            <>
              <div className="flex items-center justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    if (currentPageBefore > 0) {
                      setCurrentPageBefore((prev) => prev - 1);
                    }
                  }}
                  disabled={currentPageBefore === 0}
                >
                  <ChevronLeft className="h-4 w-4" />前
                </Button>
                <span>
                  {currentPageBefore + 1} / {imagesBefore.length}
                </span>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    if (currentPageBefore < imagesBefore.length - 1) {
                      setCurrentPageBefore((prev) => prev + 1);
                    }
                  }}
                  disabled={currentPageBefore === imagesBefore.length - 1}
                >
                  次
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              <Image
                src={imagesBefore[currentPageBefore]}
                alt="before"
                width={500}
                height={500}
                className="w-full max-w-xl border"
              />
            </>
          )}

          <Button
            type="submit"
            disabled={
              imagesBefore.length === 0 || isAnalyzing || isLoadingImages
            }
            className="w-full max-w-xl"
          >
            {isAnalyzing
              ? "解析中..."
              : isLoadingImages
              ? "PDF変換中..."
              : "ファイルを解析"}
          </Button>
        </form>
        <form
          onSubmit={handleSubmitAfter}
          className="flex flex-col gap-6 max-w-md"
        >
          <h2 className="text-xl font-bold">校正後ファイル</h2>
          <FileUploader
            files={filesAfter}
            handleFileChange={(e) => handleFileChange(e, "after")}
          />
          {imagesAfter.length > 0 && (
            <>
              <div className="flex items-center justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    if (currentPageAfter > 0) {
                      setCurrentPageAfter((prev) => prev - 1);
                    }
                  }}
                  disabled={currentPageAfter === 0}
                >
                  <ChevronLeft className="h-4 w-4" />前
                </Button>
                <span>
                  {currentPageAfter + 1} / {imagesAfter.length}
                </span>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    if (currentPageAfter < imagesAfter.length - 1) {
                      setCurrentPageAfter((prev) => prev + 1);
                    }
                  }}
                  disabled={currentPageAfter === imagesAfter.length - 1}
                >
                  次
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              <Image
                src={imagesAfter[currentPageAfter]}
                alt="after"
                width={500}
                height={500}
                className="w-full max-w-xl border"
              />
            </>
          )}

          <Button
            type="submit"
            disabled={
              imagesAfter.length === 0 || isAnalyzing || isLoadingImages
            }
            className="w-full max-w-xl"
          >
            {isAnalyzing
              ? "解析中..."
              : isLoadingImages
              ? "PDF変換中..."
              : "ファイルを解析"}
          </Button>
        </form>
      </div>

      <MessageDisplay error={error} />
      <MessageDisplay error={JSON.stringify(analysis, null, 2)} />
    </div>
  );
}
